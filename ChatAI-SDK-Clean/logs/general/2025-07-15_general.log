{"timestamp":"2025-07-15T05:30:24.017Z","type":"WARNING","level":"WARN","context":"Document Processing","message":"Document has unusually high filtering rate","additionalInfo":{"filename":"test-document.pdf","filteringRate":0.35,"threshold":0.25}}
{"timestamp":"2025-07-15T05:30:24.017Z","type":"ERROR","level":"ERROR","context":"Document Processing","error":{"message":"Simulated processing error for testing","stack":"Error: Simulated processing error for testing\n    at LoggingTester.testErrorAndWarningLogs (/data/RAG/under_construction/ChatAI-SDK-Clean/test-logging.js:185:13)\n    at LoggingTester.runAllTests (/data/RAG/under_construction/ChatAI-SDK-Clean/test-logging.js:282:10)\n    at Object.<anonymous> (/data/RAG/under_construction/ChatAI-SDK-Clean/test-logging.js:305:12)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)","name":"Error"},"additionalInfo":{"filename":"test-document.pdf","step":"chunk-filtering","chunkIndex":15}}
