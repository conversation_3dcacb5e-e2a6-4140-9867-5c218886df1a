{"version": 3, "names": ["_index", "require", "validate", "node", "key", "val", "fields", "NODE_FIELDS", "type", "field", "validateField", "validate<PERSON><PERSON><PERSON>", "validateInternal", "maybeNode", "optional", "_NODE_PARENT_VALIDATI", "NODE_PARENT_VALIDATIONS", "call", "_NODE_PARENT_VALIDATI2"], "sources": ["../../src/validators/validate.ts"], "sourcesContent": ["import {\n  NODE_FIELDS,\n  NODE_PARENT_VALIDATIONS,\n  type FieldOptions,\n} from \"../definitions/index.ts\";\nimport type * as t from \"../index.ts\";\n\nexport default function validate(\n  node: t.Node | undefined | null,\n  key: string,\n  val: unknown,\n): void {\n  if (!node) return;\n\n  const fields = NODE_FIELDS[node.type];\n  if (!fields) return;\n\n  const field = fields[key];\n  validateField(node, key, val, field);\n  validateChild(node, key, val);\n}\n\nexport function validateInternal(\n  field: FieldOptions,\n  node: t.Node | undefined | null,\n  key: string,\n  val: unknown,\n  maybeNode?: 1,\n): void {\n  if (!field?.validate) return;\n  if (field.optional && val == null) return;\n\n  field.validate(node, key, val);\n\n  if (maybeNode) {\n    const type = (val as t.Node).type;\n    if (type == null) return;\n    NODE_PARENT_VALIDATIONS[type]?.(node, key, val);\n  }\n}\n\nexport function validateField(\n  node: t.Node | undefined | null,\n  key: string,\n  val: unknown,\n  field: FieldOptions | undefined | null,\n): void {\n  if (!field?.validate) return;\n  if (field.optional && val == null) return;\n\n  field.validate(node, key, val);\n}\n\nexport function validateChild(\n  node: t.Node | undefined | null,\n  key: string | { toString(): string },\n  val?: unknown,\n) {\n  const type = (val as t.Node)?.type;\n  if (type == null) return;\n  NODE_PARENT_VALIDATIONS[type]?.(node, key, val);\n}\n"], "mappings": ";;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAOe,SAASC,QAAQA,CAC9BC,IAA+B,EAC/BC,GAAW,EACXC,GAAY,EACN;EACN,IAAI,CAACF,IAAI,EAAE;EAEX,MAAMG,MAAM,GAAGC,kBAAW,CAACJ,IAAI,CAACK,IAAI,CAAC;EACrC,IAAI,CAACF,MAAM,EAAE;EAEb,MAAMG,KAAK,GAAGH,MAAM,CAACF,GAAG,CAAC;EACzBM,aAAa,CAACP,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEI,KAAK,CAAC;EACpCE,aAAa,CAACR,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;AAC/B;AAEO,SAASO,gBAAgBA,CAC9BH,KAAmB,EACnBN,IAA+B,EAC/BC,GAAW,EACXC,GAAY,EACZQ,SAAa,EACP;EACN,IAAI,EAACJ,KAAK,YAALA,KAAK,CAAEP,QAAQ,GAAE;EACtB,IAAIO,KAAK,CAACK,QAAQ,IAAIT,GAAG,IAAI,IAAI,EAAE;EAEnCI,KAAK,CAACP,QAAQ,CAACC,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;EAE9B,IAAIQ,SAAS,EAAE;IAAA,IAAAE,qBAAA;IACb,MAAMP,IAAI,GAAIH,GAAG,CAAYG,IAAI;IACjC,IAAIA,IAAI,IAAI,IAAI,EAAE;IAClB,CAAAO,qBAAA,GAAAC,8BAAuB,CAACR,IAAI,CAAC,aAA7BO,qBAAA,CAAAE,IAAA,CAAAD,8BAAuB,EAASb,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;EACjD;AACF;AAEO,SAASK,aAAaA,CAC3BP,IAA+B,EAC/BC,GAAW,EACXC,GAAY,EACZI,KAAsC,EAChC;EACN,IAAI,EAACA,KAAK,YAALA,KAAK,CAAEP,QAAQ,GAAE;EACtB,IAAIO,KAAK,CAACK,QAAQ,IAAIT,GAAG,IAAI,IAAI,EAAE;EAEnCI,KAAK,CAACP,QAAQ,CAACC,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;AAChC;AAEO,SAASM,aAAaA,CAC3BR,IAA+B,EAC/BC,GAAoC,EACpCC,GAAa,EACb;EAAA,IAAAa,sBAAA;EACA,MAAMV,IAAI,GAAIH,GAAG,oBAAHA,GAAG,CAAaG,IAAI;EAClC,IAAIA,IAAI,IAAI,IAAI,EAAE;EAClB,CAAAU,sBAAA,GAAAF,8BAAuB,CAACR,IAAI,CAAC,aAA7BU,sBAAA,CAAAD,IAAA,CAAAD,8BAAuB,EAASb,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;AACjD", "ignoreList": []}