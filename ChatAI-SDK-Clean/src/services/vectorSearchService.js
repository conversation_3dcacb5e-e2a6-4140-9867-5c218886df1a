const qdrantService = require('./qdrantService');

class VectorSearchService {
  constructor() {
    this.isInitialized = false;
  }

  /**
   * Initialize the vector search service
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      await qdrantService.initialize();
      this.isInitialized = true;
      console.log('✅ VectorSearchService initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize VectorSearchService:', error.message);
      throw error;
    }
  }

  /**
   * Retrieve relevant context from documents using vector search
   * @param {Array} documents - Array of document objects from User Service
   * @param {string} query - Search query
   * @param {string} appId - Application ID for tenant isolation
   * @returns {Promise<string>} Concatenated relevant context
   */
  async retrieveFromMultipleDocuments(documents, query, appId) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      console.log(`\n🔍 ═══════════════════════════════════════════════════════════════`);
      console.log(`🔍 VECTOR SEARCH STARTED`);
      console.log(`🔍 Query: "${query}"`);
      console.log(`🔍 Documents: ${documents.length}`);
      console.log(`🔍 AppId: ${appId}`);
      console.log(`🔍 ═══════════════════════════════════════════════════════════════\n`);

      // Search for relevant chunks in Qdrant with intelligent thresholds
      console.log(`🔍 Searching Qdrant for similar chunks...`);
      const searchLimit = 15; // Increased for better coverage
      const baseThreshold = 0.3; // Base threshold for initial search
      const relevantChunks = await qdrantService.searchSimilarChunks(query, appId, searchLimit, baseThreshold);
      console.log(`📊 Qdrant returned ${relevantChunks.length} chunks (limit: ${searchLimit}, threshold: ${baseThreshold})`);

      // Apply intelligent quality filtering
      const qualityFilteredChunks = this.applyQualityFiltering(relevantChunks, query);
      console.log(`🎯 After quality filtering: ${qualityFilteredChunks.length} chunks (removed ${relevantChunks.length - qualityFilteredChunks.length} low-quality matches)`);

      if (qualityFilteredChunks.length === 0) {
        console.log('❌ No relevant chunks found in vector database after quality filtering');
        console.log('🚫 Fallback methods disabled - using pure vector search only');
        console.log('💡 Tip: Check if embeddings are properly generated and stored');
        return '';
      }

      // FALLBACK METHODS COMMENTED OUT FOR PURE VECTOR SEARCH TESTING
      /*
      if (relevantChunks.length === 0) {
        console.log('⚠️ No relevant chunks found in vector database, trying fallback...');

        // Fallback: Get all chunks for this appId and do basic text matching
        console.log(`🔄 Trying fallback: Getting all chunks for appId: ${appId}`);
        const allChunks = await qdrantService.getAllDocumentChunks(appId, 20);
        console.log(`📊 Fallback found ${allChunks.length} total chunks for appId`);

        if (allChunks.length === 0) {
          console.log('⚠️ No document chunks found for this appId in vector database');
          console.log('🔄 Using document-based fallback context...');
          return this.createFallbackContext(documents, query);
        }

        // Simple text-based relevance scoring
        const queryLower = query.toLowerCase();
        const scoredChunks = allChunks.map(chunk => {
          const textLower = chunk.text.toLowerCase();
          let score = 0;

          // Basic keyword matching
          const queryWords = queryLower.split(' ').filter(word => word.length > 2);
          queryWords.forEach(word => {
            if (textLower.includes(word)) {
              score += 1;
            }
          });

          // Boost score for certain keywords
          if (textLower.includes('chatai') || textLower.includes('vector') ||
              textLower.includes('database') || textLower.includes('integration')) {
            score += 2;
          }

          return { ...chunk, score };
        });

        // Sort by score and take top results
        const topChunks = scoredChunks
          .filter(chunk => chunk.score > 0)
          .sort((a, b) => b.score - a.score)
          .slice(0, 5);

        if (topChunks.length > 0) {
          console.log(`✅ Found ${topChunks.length} relevant chunks using fallback text matching`);
          console.log(`📝 Top chunks scores: ${topChunks.map(c => c.score).join(', ')}`);
          return this.formatContext(topChunks);
        }

        console.log('⚠️ No relevant content found, using document fallback');
        return this.createFallbackContext(documents, query);
      }
      */

      console.log(`✅ Found ${qualityFilteredChunks.length} relevant chunks from vector search`);
      console.log(`📝 Vector search successful - formatting context...`);
      return this.formatContext(qualityFilteredChunks);

    } catch (error) {
      console.error('❌ Vector search error:', error.message);
      console.error('🚫 Error fallback disabled - pure vector search only');
      console.log(`\n🔍 ═══════════════════════════════════════════════════════════════`);
      console.log(`🔍 VECTOR SEARCH FAILED - NO FALLBACK`);
      console.log(`🔍 Error: ${error.message}`);
      console.log(`🔍 ═══════════════════════════════════════════════════════════════\n`);
      throw error; // Re-throw to see the actual error
    }
  }

  /**
   * Format chunks into optimized context string for LLM consumption
   * @param {Array} chunks - Array of document chunks with scores
   * @returns {string} Formatted context optimized for AI understanding
   */
  formatContext(chunks) {
    if (!chunks || chunks.length === 0) {
      return '';
    }

    // Sort chunks by relevance score (highest first)
    const sortedChunks = chunks.sort((a, b) => (b.score || 0) - (a.score || 0));

    // Group chunks by document for better context flow
    const chunksByDocument = this.groupChunksByDocument(sortedChunks);

    const contextParts = [];
    let totalLength = 0;
    const maxContextLength = 8000; // Optimal context length for most models

    console.log(`🔍 DEBUG: Processing ${Object.keys(chunksByDocument).length} document groups`);

    for (const [documentName, documentChunks] of Object.entries(chunksByDocument)) {
      console.log(`🔍 DEBUG: Processing document "${documentName}" with ${documentChunks.length} chunks`);

      // Create document section header (clean, no technical details)
      const documentHeader = `=== ${this.cleanDocumentName(documentName)} ===`;
      let documentSection = documentHeader + '\n';

      // Add chunks one by one until we hit the limit
      let addedChunks = 0;
      for (const chunk of documentChunks) {
        const chunkText = chunk.text.trim();
        const chunkWithSpacing = chunkText + '\n\n';

        // Check if adding this chunk would exceed the limit
        if (totalLength + documentSection.length + chunkWithSpacing.length > maxContextLength) {
          console.log(`📏 Context length limit reached (${maxContextLength} chars), stopping after ${addedChunks} chunks from ${documentName}`);
          break;
        }

        documentSection += chunkWithSpacing;
        addedChunks++;
      }

      // Only add the document section if we added at least one chunk
      if (addedChunks > 0) {
        contextParts.push(documentSection);
        totalLength += documentSection.length;
        console.log(`🔍 DEBUG: Added ${addedChunks}/${documentChunks.length} chunks from "${documentName}", new total: ${totalLength} chars`);
      } else {
        console.log(`⚠️ Could not add any chunks from "${documentName}" due to length constraints`);
        break; // If we can't add any chunks from this document, we're done
      }
    }

    const context = contextParts.join('\n');

    // Enhanced logging with quality metrics
    console.log(`📝 Generated optimized context:`);
    console.log(`   📊 Length: ${context.length} characters (${Math.round(context.length / maxContextLength * 100)}% of limit)`);
    console.log(`   📄 Documents: ${Object.keys(chunksByDocument).length}`);
    console.log(`   🧩 Chunks: ${chunks.length} total`);
    console.log(`   🎯 Avg relevance: ${this.calculateAverageRelevance(chunks).toFixed(3)}`);
    console.log(`   🔍 Top chunk score: ${chunks[0]?.score?.toFixed(3) || 'N/A'}`);

    console.log(`\n🔍 ═══════════════════════════════════════════════════════════════`);
    console.log(`🔍 OPTIMIZED VECTOR SEARCH COMPLETED`);
    console.log(`🔍 Context quality: ${this.assessContextQuality(chunks, context)}`);
    console.log(`🔍 ═══════════════════════════════════════════════════════════════\n`);

    return context;
  }

  /**
   * Group chunks by document for better context organization
   */
  groupChunksByDocument(chunks) {
    const grouped = {};

    chunks.forEach(chunk => {
      const metadata = chunk.metadata || {};
      const filename = metadata.filename || 'Unknown Document';

      if (!grouped[filename]) {
        grouped[filename] = [];
      }
      grouped[filename].push(chunk);
    });

    return grouped;
  }

  /**
   * Clean document name for better presentation
   */
  cleanDocumentName(filename) {
    return filename
      .replace(/\.[^/.]+$/, '') // Remove file extension
      .replace(/[-_]/g, ' ')    // Replace dashes/underscores with spaces
      .replace(/\b\w/g, l => l.toUpperCase()); // Title case
  }

  /**
   * Combine related chunks from the same document intelligently
   */
  combineRelatedChunks(chunks) {
    if (chunks.length === 1) {
      return chunks[0].text;
    }

    // Sort chunks by their original order in document (if available)
    const sortedChunks = chunks.sort((a, b) => {
      const aIndex = a.metadata?.chunkIndex || 0;
      const bIndex = b.metadata?.chunkIndex || 0;
      return aIndex - bIndex;
    });

    // Combine chunks with smart spacing
    return sortedChunks.map(chunk => {
      const text = chunk.text.trim();
      const score = chunk.score ? ` [Relevance: ${chunk.score.toFixed(2)}]` : '';
      return text;
    }).join('\n\n');
  }

  /**
   * Apply intelligent quality filtering to remove false positives
   */
  applyQualityFiltering(chunks, query) {
    if (!chunks.length) return [];

    // Calculate dynamic threshold based on score distribution
    const scores = chunks.map(chunk => chunk.score).sort((a, b) => b - a);
    const topScore = scores[0];
    const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;

    // Dynamic threshold: require at least 70% of top score, minimum 0.65
    const dynamicThreshold = Math.max(0.65, topScore * 0.7);

    console.log(`🎯 Quality filtering: top=${topScore.toFixed(3)}, avg=${avgScore.toFixed(3)}, threshold=${dynamicThreshold.toFixed(3)}`);

    // Filter chunks that meet quality criteria
    const qualityChunks = chunks.filter(chunk => {
      const score = chunk.score;
      const text = chunk.text || '';

      // Score-based filtering
      if (score < dynamicThreshold) {
        console.log(`🚫 Filtered chunk (score ${score.toFixed(3)} < ${dynamicThreshold.toFixed(3)}): "${text.substring(0, 50)}..."`);
        return false;
      }

      // Content quality filtering
      if (text.length < 50) {
        console.log(`🚫 Filtered chunk (too short): "${text}"`);
        return false;
      }

      return true;
    });

    return qualityChunks;
  }

  /**
   * Calculate average relevance score
   */
  calculateAverageRelevance(chunks) {
    if (!chunks.length) return 0;
    const totalScore = chunks.reduce((sum, chunk) => sum + (chunk.score || 0), 0);
    return totalScore / chunks.length;
  }

  /**
   * Assess overall context quality
   */
  assessContextQuality(chunks, context) {
    const avgRelevance = this.calculateAverageRelevance(chunks);
    const contextLength = context.length;
    const chunkCount = chunks.length;

    if (avgRelevance > 0.8 && contextLength > 1000) return 'EXCELLENT';
    if (avgRelevance > 0.6 && contextLength > 500) return 'GOOD';
    if (avgRelevance > 0.4 && contextLength > 200) return 'FAIR';
    return 'POOR';
  }

  /**
   * Create fallback context from document metadata when vector search fails
   * @param {Array} documents - Document objects from User Service
   * @param {string} query - Search query
   * @returns {string} Fallback context
   */
  createFallbackContext(documents, query) {
    if (!documents || documents.length === 0) {
      return '';
    }

    console.log(`🔄 Creating fallback context from ${documents.length} document(s)`);

    const contextParts = documents.map(doc => {
      const filename = doc.filename || 'Unknown Document';
      const parsedText = doc.parsedText || doc.parsedData || '';

      if (parsedText) {
        // Truncate very long documents
        const truncatedText = parsedText.length > 2000 ?
          parsedText.substring(0, 2000) + '...' : parsedText;

        return `--- Document: ${filename} ---\n${truncatedText}\n`;
      } else {
        return `--- Document: ${filename} ---\nDocument content not available for search.\n`;
      }
    });

    const context = contextParts.join('\n');
    console.log(`📝 Generated fallback context: ${context.length} characters`);

    return context;
  }

  /**
   * Get vector database statistics
   * @returns {Promise<Object>} Database statistics
   */
  async getStats() {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const collectionInfo = await qdrantService.getCollectionInfo();
      return {
        collection: collectionInfo.result?.config?.params || {},
        pointsCount: collectionInfo.result?.points_count || 0,
        status: collectionInfo.result?.status || 'unknown'
      };
    } catch (error) {
      console.error('❌ Failed to get vector database stats:', error.message);
      return {
        error: error.message,
        status: 'error'
      };
    }
  }
}

module.exports = new VectorSearchService();
