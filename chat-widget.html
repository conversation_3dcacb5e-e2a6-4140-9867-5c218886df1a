<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatAI Widget Demo</title>
    <style>
        /* Demo page styling */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 40px;
            background: #f5f5f5;
            min-height: 100vh;
        }

        .demo-content {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
        }

        p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 16px;
        }

        /* Chat Widget Styles */
        .chat-widget {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }

        .chat-widget-button {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            color: white;
            font-size: 24px;
        }

        .chat-widget-button:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
        }

        .chat-widget-button.active {
            background: #dc3545;
        }

        .chat-widget-button.active:hover {
            box-shadow: 0 6px 25px rgba(220, 53, 69, 0.6);
        }

        /* Chat Interface Overlay */
        .chat-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
            align-items: center;
            justify-content: center;
            padding: 20px;
            box-sizing: border-box;
        }

        .chat-overlay.active {
            display: flex;
        }

        .chat-iframe-container {
            width: 100%;
            max-width: 900px;
            height: 93vh;
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }

        .chat-iframe {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 16px;
        }

        .chat-close-button {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.1);
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 18px;
            z-index: 1001;
            transition: all 0.2s ease;
        }

        .chat-close-button:hover {
            background: rgba(0, 0, 0, 0.2);
            color: #333;
        }

        /* Notification badge */
        .chat-notification {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 20px;
            height: 20px;
            background: #dc3545;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .chat-iframe-container {
                width: 100%;
                height: 100vh;
                border-radius: 0;
                max-width: none;
            }

            .chat-iframe {
                border-radius: 0;
            }

            .chat-overlay {
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Demo page content -->
    <div class="demo-content">
        <h1>🚀 ChatAI Widget Demo</h1>
        <p>This is a demo page showing how the ChatAI widget can be integrated into any website. The floating chat button appears in the bottom-right corner.</p>
        
        <p>Click the chat button to open the AI assistant interface. The widget provides:</p>
        <ul>
            <li>✨ Floating chat button with hover effects</li>
            <li>🎯 Full-screen chat interface overlay</li>
            <li>📱 Mobile-responsive design</li>
            <li>🔔 Optional notification badge</li>
            <li>⚡ Easy integration with any website</li>
        </ul>

        <p>The chat interface loads in an iframe, keeping it isolated from your main page while providing seamless user experience.</p>

        <h2>Integration</h2>
        <p>To integrate this widget into your website, simply include the widget HTML and CSS in your page. The widget is completely self-contained and won't interfere with your existing styles.</p>
    </div>

    <!-- Chat Widget -->
    <div class="chat-widget">
        <button class="chat-widget-button" id="chatButton">
            <span id="chatIcon">💬</span>
        </button>
        <!-- Optional notification badge -->
        <div class="chat-notification" id="chatNotification" style="display: none;">1</div>
    </div>

    <!-- Chat Overlay -->
    <div class="chat-overlay" id="chatOverlay">
        <div class="chat-iframe-container">
            <button class="chat-close-button" id="chatCloseButton">×</button>
            <iframe class="chat-iframe" id="chatIframe" src=""></iframe>
        </div>
    </div>

    <script>
        // Chat widget functionality
        const chatButton = document.getElementById('chatButton');
        const chatOverlay = document.getElementById('chatOverlay');
        const chatCloseButton = document.getElementById('chatCloseButton');
        const chatIframe = document.getElementById('chatIframe');
        const chatIcon = document.getElementById('chatIcon');
        const chatNotification = document.getElementById('chatNotification');

        let isChatOpen = false;

        // Chat interface URL (update this to your actual streaming UI URL)
        const CHAT_URL = './streaming-ui.html';

        function openChat() {
            if (!isChatOpen) {
                chatIframe.src = CHAT_URL;
                chatOverlay.classList.add('active');
                chatButton.classList.add('active');
                chatIcon.textContent = '×';
                isChatOpen = true;
                
                // Hide notification badge when chat is opened
                chatNotification.style.display = 'none';
                
                // Prevent body scroll when chat is open
                document.body.style.overflow = 'hidden';
            }
        }

        function closeChat() {
            if (isChatOpen) {
                chatOverlay.classList.remove('active');
                chatButton.classList.remove('active');
                chatIcon.textContent = '💬';
                isChatOpen = false;
                
                // Re-enable body scroll
                document.body.style.overflow = '';
                
                // Optional: Clear iframe src to stop any ongoing processes
                setTimeout(() => {
                    if (!isChatOpen) {
                        chatIframe.src = '';
                    }
                }, 300);
            }
        }

        function toggleChat() {
            if (isChatOpen) {
                closeChat();
            } else {
                openChat();
            }
        }

        // Event listeners
        chatButton.addEventListener('click', toggleChat);
        chatCloseButton.addEventListener('click', closeChat);

        // Close chat when clicking outside the iframe container
        chatOverlay.addEventListener('click', function(e) {
            if (e.target === chatOverlay) {
                closeChat();
            }
        });

        // Close chat with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && isChatOpen) {
                closeChat();
            }
        });

        // Optional: Show notification badge (simulate new message)
        // Uncomment the line below to test the notification feature
        // setTimeout(() => { chatNotification.style.display = 'flex'; }, 3000);
    </script>
</body>
</html>
