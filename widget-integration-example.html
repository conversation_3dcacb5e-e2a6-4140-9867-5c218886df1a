<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Easy Widget Integration Example</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 40px;
            background: #f8f9fa;
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
        }

        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
        }

        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', monospace;
            font-size: 14px;
        }

        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
            border-radius: 4px;
        }

        .feature-list {
            background: #d4edda;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .feature-list ul {
            margin: 0;
            padding-left: 20px;
        }

        .feature-list li {
            margin: 8px 0;
        }

        .demo-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: background 0.2s;
        }

        .demo-button:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 ChatAI Widget Integration</h1>
        
        <p>This page demonstrates how easy it is to integrate the ChatAI widget into any website. The widget is already active on this page - look for the floating chat button!</p>

        <div class="feature-list">
            <h3>✨ Widget Features:</h3>
            <ul>
                <li>🎯 <strong>Floating chat button</strong> with smooth animations</li>
                <li>📱 <strong>Mobile responsive</strong> design</li>
                <li>🎨 <strong>Customizable positioning</strong> (4 corners)</li>
                <li>🔔 <strong>Notification badges</strong> for new messages</li>
                <li>⚡ <strong>Easy integration</strong> - just 2 lines of code!</li>
                <li>🛡️ <strong>Isolated iframe</strong> - won't affect your site</li>
                <li>⌨️ <strong>Keyboard shortcuts</strong> (ESC to close)</li>
                <li>🎭 <strong>Customizable themes</strong> and colors</li>
            </ul>
        </div>

        <h2>📋 Integration Methods</h2>

        <h3>Method 1: Auto-initialization (Easiest)</h3>
        <p>Just include the script with data attributes:</p>
        <div class="code-block">
&lt;script src="chatai-widget.js" 
        data-auto-init="true"
        data-chat-url="./streaming-ui.html"
        data-position="bottom-right"
        data-show-notification="true"&gt;&lt;/script&gt;
        </div>

        <h3>Method 2: Manual initialization</h3>
        <p>Include the script and initialize with custom options:</p>
        <div class="code-block">
&lt;script src="chatai-widget.js"&gt;&lt;/script&gt;
&lt;script&gt;
    ChatAIWidget.init({
        chatUrl: './streaming-ui.html',
        position: 'bottom-right',
        showNotification: true,
        buttonText: '🤖',
        closeText: '✕'
    });
&lt;/script&gt;
        </div>

        <h3>Method 3: Advanced usage with instance</h3>
        <div class="code-block">
&lt;script&gt;
    const chatWidget = new ChatAIWidget({
        chatUrl: './streaming-ui.html',
        position: 'bottom-left',
        theme: 'custom'
    });
    
    // Show notification programmatically
    setTimeout(() => {
        chatWidget.showNotification(3);
    }, 5000);
&lt;/script&gt;
        </div>

        <h2>⚙️ Configuration Options</h2>
        <div class="code-block">
{
    chatUrl: './streaming-ui.html',     // URL to your chat interface
    position: 'bottom-right',           // bottom-right, bottom-left, top-right, top-left
    showNotification: false,            // Show notification badge
    buttonText: '💬',                   // Chat button icon/text
    closeText: '×',                     // Close button text
    theme: 'default'                    // Theme (for future customization)
}
        </div>

        <div class="highlight">
            <strong>💡 Pro Tip:</strong> The widget loads the chat interface in an iframe, keeping it completely isolated from your main website. This means no CSS conflicts and better security!
        </div>

        <h2>🎮 Demo Controls</h2>
        <p>Try these demo functions:</p>
        <button class="demo-button" onclick="showNotificationDemo()">Show Notification</button>
        <button class="demo-button" onclick="hideNotificationDemo()">Hide Notification</button>
        <button class="demo-button" onclick="openChatDemo()">Open Chat</button>
        <button class="demo-button" onclick="closeChatDemo()">Close Chat</button>

        <h2>📱 Mobile Support</h2>
        <p>The widget automatically adapts to mobile devices:</p>
        <ul>
            <li>Full-screen chat interface on mobile</li>
            <li>Touch-friendly button sizing</li>
            <li>Responsive positioning</li>
            <li>Optimized for both portrait and landscape</li>
        </ul>

        <h2>🔧 Customization</h2>
        <p>You can easily customize the widget by modifying the CSS variables or extending the ChatAIWidget class. The widget is designed to be flexible and adaptable to your brand.</p>

        <div class="highlight">
            <strong>🚀 Ready to integrate?</strong> Copy one of the integration methods above and paste it into your website. The widget will automatically appear and be ready to use!
        </div>
    </div>

    <!-- ChatAI Widget Integration -->
    <script src="chatai-widget.js"></script>
    <script>
        // Initialize the widget
        const widget = ChatAIWidget.init({
            chatUrl: './streaming-ui.html',
            position: 'bottom-right',
            showNotification: false,
            buttonText: '💬',
            closeText: '×'
        });

        // Demo functions
        function showNotificationDemo() {
            widget.showNotification(Math.floor(Math.random() * 9) + 1);
        }

        function hideNotificationDemo() {
            widget.hideNotification();
        }

        function openChatDemo() {
            widget.openChat();
        }

        function closeChatDemo() {
            widget.closeChat();
        }

        // Auto-show notification after 3 seconds for demo
        setTimeout(() => {
            widget.showNotification(1);
        }, 3000);
    </script>
</body>
</html>
